import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/link.dart';
import '../models/collection.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  static Database? _database;

  factory DatabaseService() => _instance;

  DatabaseService._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final path = join(await getDatabasesPath(), 'link_saver.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create links table
    await db.execute('''
      CREATE TABLE links(
        id TEXT PRIMARY KEY,
        url TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        imageUrl TEXT,
        favicon TEXT,
        createdAt INTEGER NOT NULL,
        lastVisited INTEGER,
        tags TEXT,
        collectionId TEXT,
        isFavorite INTEGER NOT NULL DEFAULT 0,
        color INTEGER
      )
    ''');

    // Create collections table
    await db.execute('''
      CREATE TABLE collections(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        color INTEGER NOT NULL,
        icon INTEGER NOT NULL,
        iconFontFamily TEXT,
        iconFontPackage TEXT,
        createdAt INTEGER NOT NULL
      )
    ''');
  }

  // Link operations
  Future<List<Link>> getLinks() async {
    final db = await database;
    final maps = await db.query('links');
    return List.generate(maps.length, (i) => Link.fromMap(maps[i]));
  }

  Future<Link?> getLink(String id) async {
    final db = await database;
    final maps = await db.query('links', where: 'id = ?', whereArgs: [id]);
    if (maps.isNotEmpty) {
      return Link.fromMap(maps.first);
    }
    return null;
  }

  Future<void> insertLink(Link link) async {
    final db = await database;
    await db.insert('links', link.toMap());
  }

  Future<void> updateLink(Link link) async {
    final db = await database;
    await db.update(
      'links',
      link.toMap(),
      where: 'id = ?',
      whereArgs: [link.id],
    );
  }

  Future<void> deleteLink(String id) async {
    final db = await database;
    await db.delete('links', where: 'id = ?', whereArgs: [id]);
  }

  // Collection operations
  Future<List<Collection>> getCollections() async {
    final db = await database;
    final maps = await db.query('collections');
    return List.generate(maps.length, (i) {
      final collection = Collection.fromMap(maps[i]);
      return collection;
    });
  }

  Future<Collection?> getCollection(String id) async {
    final db = await database;
    final maps = await db.query('collections', where: 'id = ?', whereArgs: [id]);
    if (maps.isNotEmpty) {
      return Collection.fromMap(maps.first);
    }
    return null;
  }

  Future<void> insertCollection(Collection collection) async {
    final db = await database;
    await db.insert('collections', collection.toMap());
  }

  Future<void> updateCollection(Collection collection) async {
    final db = await database;
    await db.update(
      'collections',
      collection.toMap(),
      where: 'id = ?',
      whereArgs: [collection.id],
    );
  }

  Future<void> deleteCollection(String id) async {
    final db = await database;
    await db.delete('collections', where: 'id = ?', whereArgs: [id]);
  }

  // Get link count for a collection
  Future<int> getLinkCountForCollection(String collectionId) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM links WHERE collectionId = ?',
      [collectionId],
    );
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // Update link counts for all collections
  Future<void> updateCollectionLinkCounts() async {
    final db = await database;
    final collections = await getCollections();
    
    for (final collection in collections) {
      final count = await getLinkCountForCollection(collection.id);
      final updatedCollection = collection.copyWith(linkCount: count);
      await updateCollection(updatedCollection);
    }
  }
}
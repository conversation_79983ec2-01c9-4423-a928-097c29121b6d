import 'package:flutter/material.dart';

class Link {
  final String id;
  final String url;
  final String title;
  final String? description;
  final String? imageUrl;
  final String? favicon;
  final DateTime createdAt;
  final DateTime? lastVisited;
  final List<String> tags;
  final String? collectionId;
  final bool isFavorite;
  final Color? color;

  Link({
    required this.id,
    required this.url,
    required this.title,
    this.description,
    this.imageUrl,
    this.favicon,
    required this.createdAt,
    this.lastVisited,
    this.tags = const [],
    this.collectionId,
    this.isFavorite = false,
    this.color,
  });

  Link copyWith({
    String? id,
    String? url,
    String? title,
    String? description,
    String? imageUrl,
    String? favicon,
    DateTime? createdAt,
    DateTime? lastVisited,
    List<String>? tags,
    String? collectionId,
    bool? isFavorite,
    Color? color,
  }) {
    return Link(
      id: id ?? this.id,
      url: url ?? this.url,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      favicon: favicon ?? this.favicon,
      createdAt: createdAt ?? this.createdAt,
      lastVisited: lastVisited ?? this.lastVisited,
      tags: tags ?? this.tags,
      collectionId: collectionId ?? this.collectionId,
      isFavorite: isFavorite ?? this.isFavorite,
      color: color ?? this.color,
    );
  }

  // Convert Link to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'url': url,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'favicon': favicon,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastVisited': lastVisited?.millisecondsSinceEpoch,
      'tags': tags.join(','),
      'collectionId': collectionId,
      'isFavorite': isFavorite ? 1 : 0,
      'color': color?.value,
    };
  }

  // Create Link from Map
  factory Link.fromMap(Map<String, dynamic> map) {
    return Link(
      id: map['id'],
      url: map['url'],
      title: map['title'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      favicon: map['favicon'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      lastVisited: map['lastVisited'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastVisited'])
          : null,
      tags: map['tags'] != null && map['tags'].isNotEmpty
          ? map['tags'].split(',')
          : [],
      collectionId: map['collectionId'],
      isFavorite: map['isFavorite'] == 1,
      color: map['color'] != null ? Color(map['color']) : null,
    );
  }

  // Get domain from URL
  String get domain {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return url;
    }
  }

  // Get formatted creation date
  String get formattedCreatedAt {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }
}
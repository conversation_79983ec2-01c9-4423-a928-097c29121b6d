import 'package:flutter/material.dart';
import '../models/link.dart';
import '../models/collection.dart';
import '../services/database_service.dart';
import '../utils/link_parser.dart';

class LinkProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  List<Link> _links = [];
  List<Collection> _collections = [];
  bool _isLoading = false;
  String _searchQuery = '';
  
  // Getters
  List<Link> get links => _links;
  List<Collection> get collections => _collections;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  
  // Filtered links
  List<Link> get favoriteLinks => _links.where((link) => link.isFavorite).toList();
  
  List<Link> get recentLinks {
    final sortedLinks = List<Link>.from(_links);
    sortedLinks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedLinks.take(10).toList();
  }
  
  List<Link> getLinksInCollection(String collectionId) {
    return _links.where((link) => link.collectionId == collectionId).toList();
  }
  
  List<Link> get searchResults {
    if (_searchQuery.isEmpty) return [];
    
    return _links.where((link) {
      final query = _searchQuery.toLowerCase();
      return link.title.toLowerCase().contains(query) ||
             link.url.toLowerCase().contains(query) ||
             (link.description?.toLowerCase().contains(query) ?? false) ||
             link.tags.any((tag) => tag.toLowerCase().contains(query));
    }).toList();
  }
  
  // Initialize provider
  Future<void> initialize() async {
    _setLoading(true);
    await _loadLinks();
    await _loadCollections();
    _setLoading(false);
  }
  
  // Load links from database
  Future<void> _loadLinks() async {
    try {
      _links = await _databaseService.getLinks();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading links: $e');
    }
  }
  
  // Load collections from database
  Future<void> _loadCollections() async {
    try {
      _collections = await _databaseService.getCollections();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading collections: $e');
    }
  }
  
  // Add a new link
  Future<void> addLink(String url, {String? collectionId}) async {
    _setLoading(true);
    
    try {
      // Parse link metadata
      final linkData = await LinkParser.parseUrl(url);
      
      final newLink = Link(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        url: url,
        title: linkData.title ?? 'Untitled Link',
        description: linkData.description,
        imageUrl: linkData.imageUrl,
        favicon: linkData.favicon,
        createdAt: DateTime.now(),
        collectionId: collectionId,
        tags: [],
      );
      
      await _databaseService.insertLink(newLink);
      await _loadLinks();
    } catch (e) {
      debugPrint('Error adding link: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // Update an existing link
  Future<void> updateLink(Link link) async {
    try {
      await _databaseService.updateLink(link);
      await _loadLinks();
    } catch (e) {
      debugPrint('Error updating link: $e');
    }
  }
  
  // Delete a link
  Future<void> deleteLink(String id) async {
    try {
      await _databaseService.deleteLink(id);
      await _loadLinks();
    } catch (e) {
      debugPrint('Error deleting link: $e');
    }
  }
  
  // Toggle favorite status
  Future<void> toggleFavorite(String id) async {
    final linkIndex = _links.indexWhere((link) => link.id == id);
    if (linkIndex >= 0) {
      final link = _links[linkIndex];
      final updatedLink = link.copyWith(isFavorite: !link.isFavorite);
      
      await _databaseService.updateLink(updatedLink);
      await _loadLinks();
    }
  }
  
  // Add a new collection
  Future<void> addCollection(Collection collection) async {
    try {
      await _databaseService.insertCollection(collection);
      await _loadCollections();
    } catch (e) {
      debugPrint('Error adding collection: $e');
    }
  }
  
  // Update an existing collection
  Future<void> updateCollection(Collection collection) async {
    try {
      await _databaseService.updateCollection(collection);
      await _loadCollections();
    } catch (e) {
      debugPrint('Error updating collection: $e');
    }
  }
  
  // Delete a collection
  Future<void> deleteCollection(String id) async {
    try {
      // First update all links in this collection to have no collection
      final linksInCollection = getLinksInCollection(id);
      for (final link in linksInCollection) {
        await _databaseService.updateLink(link.copyWith(collectionId: null));
      }
      
      await _databaseService.deleteCollection(id);
      await _loadCollections();
      await _loadLinks();
    } catch (e) {
      debugPrint('Error deleting collection: $e');
    }
  }
  
  // Set search query
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }
  
  // Clear search query
  void clearSearch() {
    _searchQuery = '';
    notifyListeners();
  }
  
  // Set loading state
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
}
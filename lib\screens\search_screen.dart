import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../models/link.dart';
import '../providers/link_provider.dart';
import '../widgets/link_card.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _showClearButton = false;
  
  @override
  void initState() {
    super.initState();
    _searchFocusNode.requestFocus();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final linkProvider = Provider.of<LinkProvider>(context, listen: false);
    linkProvider.setSearchQuery(_searchController.text);
    setState(() {
      _showClearButton = _searchController.text.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final linkProvider = Provider.of<LinkProvider>(context);
    final searchResults = linkProvider.searchResults;
    
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _searchController,
          focusNode: _searchFocusNode,
          decoration: InputDecoration(
            hintText: 'Search links...',
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(vertical: 12),
            suffixIcon: _showClearButton
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      linkProvider.setSearchQuery('');
                    },
                  )
                : null,
          ),
          style: theme.textTheme.titleMedium,
          textInputAction: TextInputAction.search,
          onSubmitted: (value) {
            // Already handled by listener
          },
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Column(
        children: [
          if (linkProvider.searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'Results for "${linkProvider.searchQuery}"',
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${searchResults.length} found',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          Expanded(
            child: _buildSearchResults(searchResults, linkProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(List<Link> searchResults, LinkProvider linkProvider) {
    if (_searchController.text.isEmpty) {
      return _buildSearchSuggestions(linkProvider);
    }
    
    if (searchResults.isEmpty) {
      return _buildNoResultsFound();
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: searchResults.length,
      itemBuilder: (context, index) {
        final link = searchResults[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: LinkCard(
            link: link,
            onTap: () => context.push('/link/${link.id}'),
          )
          .animate()
          .fadeIn(duration: 300.ms, delay: (50 * index).ms)
          .slideY(begin: 0.1, end: 0, duration: 300.ms, delay: (50 * index).ms),
        );
      },
    );
  }

  Widget _buildSearchSuggestions(LinkProvider linkProvider) {
    final theme = Theme.of(context);
    final recentLinks = linkProvider.links.take(5).toList();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Links',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          )
          .animate()
          .fadeIn(duration: 300.ms)
          .slideY(begin: 0.1, end: 0, duration: 300.ms),
          const SizedBox(height: 16),
          if (recentLinks.isEmpty)
            Center(
              child: Text(
                'No recent links',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: recentLinks.length,
              itemBuilder: (context, index) {
                final link = recentLinks[index];
                return ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: link.favicon != null && link.favicon!.isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              link.favicon!,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.link_rounded,
                                  color: theme.colorScheme.primary,
                                );
                              },
                            ),
                          )
                        : Icon(
                            Icons.link_rounded,
                            color: theme.colorScheme.primary,
                          ),
                  ),
                  title: Text(
                    link.title ?? link.url,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text(
                    link.domain,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    _searchController.text = link.title ?? link.url;
                    linkProvider.setSearchQuery(_searchController.text);
                  },
                )
                .animate()
                .fadeIn(duration: 300.ms, delay: (50 * index).ms)
                .slideY(begin: 0.1, end: 0, duration: 300.ms, delay: (50 * index).ms);
              },
            ),
          const SizedBox(height: 24),
          Text(
            'Search by Tags',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          )
          .animate()
          .fadeIn(duration: 300.ms, delay: 200.ms)
          .slideY(begin: 0.1, end: 0, duration: 300.ms, delay: 200.ms),
          const SizedBox(height: 16),
          _buildTagsList(linkProvider),
        ],
      ),
    );
  }

  Widget _buildTagsList(LinkProvider linkProvider) {
    final theme = Theme.of(context);
    final allTags = linkProvider.getAllTags();
    
    if (allTags.isEmpty) {
      return Center(
        child: Text(
          'No tags found',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      );
    }
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: allTags.map((tag) {
        return ActionChip(
          label: Text(tag),
          avatar: const Icon(Icons.tag, size: 16),
          onPressed: () {
            _searchController.text = tag;
            linkProvider.setSearchQuery(tag);
          },
        )
        .animate()
        .fadeIn(duration: 300.ms, delay: (100 * allTags.indexOf(tag)).ms % 500.ms)
        .scale(begin: 0.8, end: 1, duration: 300.ms, delay: (100 * allTags.indexOf(tag)).ms % 500.ms);
      }).toList(),
    );
  }

  Widget _buildNoResultsFound() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_rounded,
            size: 80,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          )
          .animate()
          .fadeIn(duration: 400.ms)
          .scale(begin: 0.5, end: 1, duration: 400.ms, curve: Curves.elasticOut),
          const SizedBox(height: 16),
          Text(
            'No results found',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          )
          .animate()
          .fadeIn(duration: 400.ms, delay: 100.ms),
          const SizedBox(height: 8),
          Text(
            'Try a different search term',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          )
          .animate()
          .fadeIn(duration: 400.ms, delay: 200.ms),
        ],
      ),
    );
  }
}
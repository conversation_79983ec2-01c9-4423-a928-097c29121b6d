import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../models/collection.dart';
import '../models/link.dart';
import '../providers/link_provider.dart';
import '../utils/link_parser.dart';
import '../utils/link_validator.dart';

class AddLinkScreen extends StatefulWidget {
  final String? linkId;

  const AddLinkScreen({super.key, this.linkId});

  @override
  State<AddLinkScreen> createState() => _AddLinkScreenState();
}

class _AddLinkScreenState extends State<AddLinkScreen> {
  final _formKey = GlobalKey<FormState>();
  final _urlController = TextEditingController();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _faviconController = TextEditingController();
  
  String? _selectedCollectionId;
  List<String> _tags = [];
  bool _isFavorite = false;
  bool _isLoading = false;
  bool _isEditMode = false;
  bool _isMetadataFetched = false;
  
  @override
  void initState() {
    super.initState();
    _isEditMode = widget.linkId != null;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeForm();
    });
  }

  @override
  void dispose() {
    _urlController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _imageUrlController.dispose();
    _faviconController.dispose();
    super.dispose();
  }

  Future<void> _initializeForm() async {
    final linkProvider = Provider.of<LinkProvider>(context, listen: false);
    
    if (_isEditMode) {
      final link = linkProvider.getLinkById(widget.linkId!);
      if (link != null) {
        _urlController.text = link.url;
        _titleController.text = link.title ?? '';
        _descriptionController.text = link.description ?? '';
        _imageUrlController.text = link.imageUrl ?? '';
        _faviconController.text = link.favicon ?? '';
        _selectedCollectionId = link.collectionId;
        _tags = List<String>.from(link.tags);
        _isFavorite = link.isFavorite;
        _isMetadataFetched = true;
      }
    } else if (linkProvider.clipboardLink.isNotEmpty) {
      // Pre-fill with clipboard link if available
      _urlController.text = linkProvider.clipboardLink;
      linkProvider.clearClipboardLink();
      _fetchMetadata();
    }
  }

  Future<void> _fetchMetadata() async {
    if (_urlController.text.isEmpty || _isMetadataFetched) return;
    
    final url = _urlController.text;
    if (!LinkValidator.isValidUrl(url)) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final metadata = await LinkParser.extractMetadata(url);
      
      if (mounted) {
        setState(() {
          _titleController.text = metadata.title ?? '';
          _descriptionController.text = metadata.description ?? '';
          _imageUrlController.text = metadata.imageUrl ?? '';
          _faviconController.text = metadata.favicon ?? '';
          _isMetadataFetched = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to fetch metadata: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _saveLink() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() {
      _isLoading = true;
    });
    
    final linkProvider = Provider.of<LinkProvider>(context, listen: false);
    
    try {
      final url = LinkValidator.ensureProtocol(_urlController.text.trim());
      final title = _titleController.text.trim();
      final description = _descriptionController.text.trim();
      final imageUrl = _imageUrlController.text.trim();
      final favicon = _faviconController.text.trim();
      
      if (_isEditMode) {
        final updatedLink = Link(
          id: widget.linkId!,
          url: url,
          title: title.isNotEmpty ? title : null,
          description: description.isNotEmpty ? description : null,
          imageUrl: imageUrl.isNotEmpty ? imageUrl : null,
          favicon: favicon.isNotEmpty ? favicon : null,
          createdAt: linkProvider.getLinkById(widget.linkId!)!.createdAt,
          lastVisited: linkProvider.getLinkById(widget.linkId!)!.lastVisited,
          tags: _tags,
          collectionId: _selectedCollectionId ?? '',
          isFavorite: _isFavorite,
          color: linkProvider.getLinkById(widget.linkId!)!.color,
        );
        
        await linkProvider.updateLink(updatedLink);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Link updated successfully')),
          );
          context.pop();
        }
      } else {
        final newLink = Link(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          url: url,
          title: title.isNotEmpty ? title : null,
          description: description.isNotEmpty ? description : null,
          imageUrl: imageUrl.isNotEmpty ? imageUrl : null,
          favicon: favicon.isNotEmpty ? favicon : null,
          createdAt: DateTime.now(),
          tags: _tags,
          collectionId: _selectedCollectionId ?? '',
          isFavorite: _isFavorite,
        );
        
        await linkProvider.addLink(newLink);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Link added successfully')),
          );
          context.pop();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    }
  }

  void _addTag(String tag) {
    if (tag.isEmpty) return;
    if (_tags.contains(tag)) return;
    
    setState(() {
      _tags.add(tag);
    });
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final linkProvider = Provider.of<LinkProvider>(context);
    final collections = linkProvider.collections;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? 'Edit Link' : 'Add Link')
          .animate()
          .fadeIn(duration: 500.ms, curve: Curves.easeOutQuad)
          .slideY(begin: -0.2, end: 0, duration: 500.ms, curve: Curves.easeOutQuad),
        actions: [
          if (!_isLoading)
            TextButton.icon(
              onPressed: _saveLink,
              icon: const Icon(Icons.check_rounded),
              label: const Text('Save'),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildUrlField(),
                    const SizedBox(height: 16),
                    _buildCollectionDropdown(collections),
                    const SizedBox(height: 16),
                    _buildTitleField(),
                    const SizedBox(height: 16),
                    _buildDescriptionField(),
                    const SizedBox(height: 16),
                    _buildImageUrlField(),
                    const SizedBox(height: 16),
                    _buildFaviconField(),
                    const SizedBox(height: 16),
                    _buildTagsField(),
                    const SizedBox(height: 16),
                    _buildFavoriteSwitch(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildUrlField() {
    return TextFormField(
      controller: _urlController,
      decoration: const InputDecoration(
        labelText: 'URL',
        hintText: 'https://example.com',
        prefixIcon: Icon(Icons.link_rounded),
      ),
      keyboardType: TextInputType.url,
      textInputAction: TextInputAction.next,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a URL';
        }
        if (!LinkValidator.isValidUrl(value)) {
          return 'Please enter a valid URL';
        }
        return null;
      },
      onEditingComplete: _fetchMetadata,
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 100.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 100.ms);
  }

  Widget _buildCollectionDropdown(List<Collection> collections) {
    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'Collection',
        prefixIcon: Icon(Icons.folder_rounded),
      ),
      value: _selectedCollectionId,
      items: [
        const DropdownMenuItem<String>(
          value: '',
          child: Text('None'),
        ),
        ...collections.map((collection) {
          return DropdownMenuItem<String>(
            value: collection.id,
            child: Row(
              children: [
                Icon(
                  IconData(
                    collection.icon,
                    fontFamily: 'MaterialIcons',
                  ),
                  color: Color(collection.color),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(collection.name),
              ],
            ),
          );
        }).toList(),
      ],
      onChanged: (value) {
        setState(() {
          _selectedCollectionId = value;
        });
      },
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 200.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 200.ms);
  }

  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: 'Title',
        hintText: 'Enter title (optional)',
        prefixIcon: Icon(Icons.title_rounded),
      ),
      textInputAction: TextInputAction.next,
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 300.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 300.ms);
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'Description',
        hintText: 'Enter description (optional)',
        prefixIcon: Icon(Icons.description_rounded),
      ),
      maxLines: 3,
      textInputAction: TextInputAction.next,
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 400.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 400.ms);
  }

  Widget _buildImageUrlField() {
    return TextFormField(
      controller: _imageUrlController,
      decoration: const InputDecoration(
        labelText: 'Image URL',
        hintText: 'Enter image URL (optional)',
        prefixIcon: Icon(Icons.image_rounded),
      ),
      keyboardType: TextInputType.url,
      textInputAction: TextInputAction.next,
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 500.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 500.ms);
  }

  Widget _buildFaviconField() {
    return TextFormField(
      controller: _faviconController,
      decoration: const InputDecoration(
        labelText: 'Favicon URL',
        hintText: 'Enter favicon URL (optional)',
        prefixIcon: Icon(Icons.favicon_rounded),
      ),
      keyboardType: TextInputType.url,
      textInputAction: TextInputAction.next,
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 600.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 600.ms);
  }

  Widget _buildTagsField() {
    final theme = Theme.of(context);
    final tagController = TextEditingController();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: theme.textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: tagController,
                decoration: const InputDecoration(
                  hintText: 'Add a tag',
                  prefixIcon: Icon(Icons.tag_rounded),
                ),
                textInputAction: TextInputAction.done,
                onFieldSubmitted: (value) {
                  if (value.isNotEmpty) {
                    _addTag(value);
                    tagController.clear();
                  }
                },
              ),
            ),
            IconButton(
              icon: const Icon(Icons.add_circle_rounded),
              onPressed: () {
                if (tagController.text.isNotEmpty) {
                  _addTag(tagController.text);
                  tagController.clear();
                }
              },
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _tags.map((tag) {
            return Chip(
              label: Text(tag),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () => _removeTag(tag),
            );
          }).toList(),
        ),
      ],
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 700.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 700.ms);
  }

  Widget _buildFavoriteSwitch() {
    final theme = Theme.of(context);
    
    return SwitchListTile(
      title: const Text('Add to Favorites'),
      secondary: Icon(
        _isFavorite ? Icons.star_rounded : Icons.star_border_rounded,
        color: _isFavorite ? Colors.amber : null,
      ),
      value: _isFavorite,
      onChanged: (value) {
        setState(() {
          _isFavorite = value;
        });
      },
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 800.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 800.ms);
  }
}
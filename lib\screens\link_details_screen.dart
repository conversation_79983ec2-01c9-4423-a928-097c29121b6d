import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/link.dart';
import '../providers/link_provider.dart';
import '../utils/link_validator.dart';

class LinkDetailsScreen extends StatelessWidget {
  final String linkId;

  const LinkDetailsScreen({super.key, required this.linkId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final linkProvider = Provider.of<LinkProvider>(context);
    final link = linkProvider.getLinkById(linkId);

    if (link == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Link Details'),
        ),
        body: Center(
          child: Text(
            'Link not found',
            style: theme.textTheme.titleLarge,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Link Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_rounded),
            onPressed: () => context.push('/add-link?id=$linkId'),
          ),
          IconButton(
            icon: Icon(
              link.isFavorite ? Icons.star_rounded : Icons.star_border_rounded,
              color: link.isFavorite ? Colors.amber : null,
            ),
            onPressed: () {
              linkProvider.toggleFavorite(link.id);
            },
          ),
          IconButton(
            icon: const Icon(Icons.share_rounded),
            onPressed: () {
              Share.share('Check out this link: ${link.url}');
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildLinkHeader(context, link),
            const SizedBox(height: 24),
            _buildLinkDetails(context, link),
            const SizedBox(height: 24),
            _buildActionButtons(context, link),
            const SizedBox(height: 24),
            if (link.tags.isNotEmpty) _buildTagsSection(context, link),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkHeader(BuildContext context, Link link) {
    final theme = Theme.of(context);
    final linkProvider = Provider.of<LinkProvider>(context);
    final collection = linkProvider.getCollectionById(link.collectionId);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (link.imageUrl != null && link.imageUrl!.isNotEmpty)
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: AspectRatio(
              aspectRatio: 16 / 9,
              child: Image.network(
                link.imageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: theme.colorScheme.surfaceVariant,
                    child: Center(
                      child: Icon(
                        Icons.image_not_supported_rounded,
                        size: 48,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                },
              ),
            ),
          )
          .animate()
          .fadeIn(duration: 500.ms)
          .slideY(begin: 0.1, end: 0, duration: 500.ms),
        const SizedBox(height: 16),
        Row(
          children: [
            if (link.favicon != null && link.favicon!.isNotEmpty)
              Container(
                width: 24,
                height: 24,
                margin: const EdgeInsets.only(right: 8),
                child: Image.network(
                  link.favicon!,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.link_rounded,
                      size: 24,
                      color: theme.colorScheme.primary,
                    );
                  },
                ),
              ),
            Expanded(
              child: Text(
                link.domain,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (collection != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Color(collection.color).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      IconData(
                        collection.icon,
                        fontFamily: 'MaterialIcons',
                      ),
                      size: 16,
                      color: Color(collection.color),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      collection.name,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Color(collection.color),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        )
        .animate()
        .fadeIn(duration: 500.ms, delay: 100.ms),
        const SizedBox(height: 8),
        Text(
          link.title ?? 'No Title',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        )
        .animate()
        .fadeIn(duration: 500.ms, delay: 200.ms),
        if (link.description != null && link.description!.isNotEmpty) ...[  
          const SizedBox(height: 8),
          Text(
            link.description!,
            style: theme.textTheme.bodyLarge,
          )
          .animate()
          .fadeIn(duration: 500.ms, delay: 300.ms),
        ],
      ],
    );
  }

  Widget _buildLinkDetails(BuildContext context, Link link) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(
              context,
              'URL',
              link.url,
              Icons.link_rounded,
              onTap: () => _copyToClipboard(context, link.url),
            ),
            const Divider(height: 24),
            _buildDetailRow(
              context,
              'Added',
              link.formattedCreatedAt,
              Icons.calendar_today_rounded,
            ),
            if (link.lastVisited != null) ...[  
              const Divider(height: 24),
              _buildDetailRow(
                context,
                'Last Visited',
                _formatDate(link.lastVisited!),
                Icons.history_rounded,
              ),
            ],
          ],
        ),
      ),
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 400.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 400.ms);
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(
                Icons.content_copy_rounded,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, Link link) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _launchUrl(link.url),
            icon: const Icon(Icons.open_in_browser_rounded),
            label: const Text('Open Link'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 16),
        OutlinedButton.icon(
          onPressed: () => _copyToClipboard(context, link.url),
          icon: const Icon(Icons.content_copy_rounded),
          label: const Text('Copy URL'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 500.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 500.ms);
  }

  Widget _buildTagsSection(BuildContext context, Link link) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: link.tags.map((tag) {
            return Chip(
              label: Text(tag),
              avatar: const Icon(Icons.tag, size: 16),
              backgroundColor: theme.colorScheme.surfaceVariant,
              side: BorderSide(
                color: theme.colorScheme.outline.withOpacity(0.2),
                width: 1,
              ),
            );
          }).toList(),
        ),
      ],
    )
    .animate()
    .fadeIn(duration: 500.ms, delay: 600.ms)
    .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 600.ms);
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(LinkValidator.ensureProtocol(url));
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
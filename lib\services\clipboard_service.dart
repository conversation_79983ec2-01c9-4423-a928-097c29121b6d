import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'package:provider/provider.dart';
import '../providers/link_provider.dart';
import '../utils/link_validator.dart';
import '../widgets/link_detected_dialog.dart';

class ClipboardService {
  static bool _isInitialized = false;
  static String _lastDetectedUrl = '';

  // Timer for clipboard checking
  static Timer? _clipboardTimer;
  
  // Initialize clipboard checker
  static void initialize(BuildContext context) {
    if (_isInitialized) return;
    _isInitialized = true;

    // Check clipboard on app start
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkClipboard(context);
      
      // Start periodic clipboard checking (every 2 seconds)
      _clipboardTimer = Timer.periodic(const Duration(seconds: 2), (_) {
        if (context.mounted) {
          _checkClipboard(context);
        }
      });
    });
  }

  // Check clipboard for valid URLs
  static Future<void> _checkClipboard(BuildContext context) async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final clipboardText = clipboardData?.text?.trim();

      if (clipboardText != null && 
          clipboardText.isNotEmpty && 
          clipboardText != _lastDetectedUrl && 
          LinkValidator.isValidUrl(clipboardText)) {
        
        _lastDetectedUrl = clipboardText;
        _showLinkDetectedDialog(context, clipboardText);
      }
    } catch (e) {
      debugPrint('Error checking clipboard: $e');
    }
  }

  // Show dialog when link is detected
  static void _showLinkDetectedDialog(BuildContext context, String url) {
    showDialog(
      context: context,
      builder: (context) => LinkDetectedDialog(url: url),
    );
  }

  // Save detected link
  static Future<void> saveDetectedLink(BuildContext context, String url, {String? collectionId}) async {
    final linkProvider = Provider.of<LinkProvider>(context, listen: false);
    await linkProvider.addLink(url, collectionId: collectionId);
  }

  // Clear last detected URL
  static void clearLastDetectedUrl() {
    _lastDetectedUrl = '';
  }
}
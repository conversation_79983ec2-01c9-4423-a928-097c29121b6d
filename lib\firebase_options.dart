// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_methods
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'placeholder-api-key',
    appId: '1:123456789012:web:1234567890123456789012',
    messagingSenderId: '123456789012',
    projectId: 'splitease-placeholder',
    authDomain: 'splitease-placeholder.firebaseapp.com',
    storageBucket: 'splitease-placeholder.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'placeholder-api-key',
    appId: '1:123456789012:android:1234567890123456789012',
    messagingSenderId: '123456789012',
    projectId: 'splitease-placeholder',
    storageBucket: 'splitease-placeholder.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'placeholder-api-key',
    appId: '1:123456789012:ios:1234567890123456789012',
    messagingSenderId: '123456789012',
    projectId: 'splitease-placeholder',
    storageBucket: 'splitease-placeholder.appspot.com',
    iosClientId: 'placeholder-ios-client-id',
    iosBundleId: 'com.example.link-saver',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'placeholder-api-key',
    appId: '1:123456789012:ios:1234567890123456789012',
    messagingSenderId: '123456789012',
    projectId: 'splitease-placeholder',
    storageBucket: 'splitease-placeholder.appspot.com',
    iosClientId: 'placeholder-ios-client-id',
    iosBundleId: 'com.example.link-saver',
  );
}
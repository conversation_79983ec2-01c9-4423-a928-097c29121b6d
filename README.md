# LinkVault - Flutter Link Management App

## Overview

LinkVault is a modern Flutter application for saving, organizing, and managing web links. The app allows users to save links from the clipboard, organize them into collections, search through saved links, and access them from anywhere.

## Features

- **Link Management**: Save, edit, and delete web links
- **Collections**: Organize links into custom collections
- **Clipboard Detection**: Automatically detect links copied to clipboard
- **Link Metadata**: Extract and display title, description, and images from links
- **Search**: Search through saved links by title, description, or URL
- **Favorites**: Mark links as favorites for quick access
- **Dark Mode**: Toggle between light and dark themes
- **Responsive Design**: Works on various screen sizes

## Technical Details

### Architecture

The app follows a provider-based architecture for state management:

- **Models**: Define data structures for links and collections
- **Providers**: Manage application state and business logic
- **Services**: Handle database operations and clipboard functionality
- **Screens**: Implement user interface components
- **Widgets**: Reusable UI components
- **Utils**: Utility functions for link validation and parsing

### Dependencies

- **State Management**: provider
- **Database**: sqflite, path_provider
- **UI Components**: flutter_slidable, google_fonts, flutter_svg, lottie
- **Animations**: flutter_animate
- **Utilities**: url_launcher, share_plus, clipboard, flutter_clipboard_listener
- **Firebase**: firebase_core, firebase_auth, cloud_firestore
- **Responsive UI**: flutter_screenutil
- **Routing**: go_router

## Getting Started

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the application

## Screenshots

[Screenshots will be added here]

## License

This project is licensed under the MIT License - see the LICENSE file for details.
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/link_provider.dart';
import '../services/clipboard_service.dart';
import '../utils/link_validator.dart';
import '../models/collection.dart';

class LinkDetectedDialog extends StatefulWidget {
  final String url;

  const LinkDetectedDialog({super.key, required this.url});

  @override
  State<LinkDetectedDialog> createState() => _LinkDetectedDialogState();
}

class _LinkDetectedDialogState extends State<LinkDetectedDialog> {
  String? _selectedCollectionId;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final linkProvider = Provider.of<LinkProvider>(context);
    final collections = linkProvider.collections;
    
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      title: Row(
        children: [
          Icon(
            Icons.link_rounded,
            color: theme.colorScheme.primary,
          )
          .animate()
          .scale(duration: 400.ms, curve: Curves.elasticOut),
          const SizedBox(width: 12),
          const Text('Link Detected!')
            .animate()
            .fadeIn(duration: 300.ms)
            .slideX(begin: 0.2, end: 0, duration: 300.ms),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'We found a link in your clipboard:',
            style: theme.textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.link_rounded,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.url,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Save to collection:',
            style: theme.textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          if (collections.isEmpty)
            Text(
              'No collections available',
              style: theme.textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            )
          else
            DropdownButtonFormField<String?>(
              value: _selectedCollectionId,
              decoration: InputDecoration(
                filled: true,
                fillColor: theme.colorScheme.surfaceVariant,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              hint: const Text('Select a collection (optional)'),
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('None'),
                ),
                ...collections.map((collection) => DropdownMenuItem<String?>(
                  value: collection.id,
                  child: Row(
                    children: [
                      Icon(collection.icon, size: 16, color: collection.color),
                      const SizedBox(width: 8),
                      Text(collection.name),
                    ],
                  ),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCollectionId = value;
                });
              },
            ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            ClipboardService.clearLastDetectedUrl();
          },
          child: const Text('Ignore'),
        ),
        ElevatedButton(
          onPressed: _isLoading
              ? null
              : () => _saveLink(context),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Save Link'),
        ),
      ],
    )
    .animate()
    .scale(begin: const Offset(0.9, 0.9), duration: 300.ms, curve: Curves.easeOutQuad)
    .fadeIn(duration: 300.ms, curve: Curves.easeOutQuad);
  }

  Future<void> _saveLink(BuildContext context) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final validUrl = LinkValidator.ensureProtocol(widget.url);
      await ClipboardService.saveDetectedLink(
        context,
        validUrl,
        collectionId: _selectedCollectionId,
      );
      
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Link saved successfully!'),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving link: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
class LinkValidator {
  // Check if a string is a valid URL
  static bool isValidUrl(String text) {
    // Basic URL validation regex
    final urlRegex = RegExp(
      r'^(https?:\/\/)?'+ // protocol
      r'((([a-z\d]([a-z\d-]*[a-z\d])*)\.)+[a-z]{2,}|'+ // domain name
      r'((\d{1,3}\.){3}\d{1,3}))'+ // OR ip (v4) address
      r'(\:\d+)?(\/[-a-z\d%_.~+]*)*'+ // port and path
      r'(\?[;&a-z\d%_.~+=-]*)?'+ // query string
      r'(\#[-a-z\d_]*)?$', // fragment locator
      caseSensitive: false,
    );
    
    return urlRegex.hasMatch(text);
  }
  
  // Ensure URL has a protocol
  static String ensureProtocol(String url) {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return 'https://$url';
    }
    return url;
  }
  
  // Extract domain from URL
  static String extractDomain(String url) {
    try {
      final uri = Uri.parse(ensureProtocol(url));
      return uri.host;
    } catch (e) {
      return url;
    }
  }
  
  // Check if URL is an image
  static bool isImageUrl(String url) {
    final imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    final lowercaseUrl = url.toLowerCase();
    
    return imageExtensions.any((ext) => lowercaseUrl.endsWith(ext));
  }
  
  // Check if URL is a video
  static bool isVideoUrl(String url) {
    final videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'];
    final lowercaseUrl = url.toLowerCase();
    
    return videoExtensions.any((ext) => lowercaseUrl.endsWith(ext));
  }
  
  // Check if URL is from a social media platform
  static bool isSocialMediaUrl(String url) {
    final socialDomains = [
      'facebook.com',
      'twitter.com',
      'instagram.com',
      'linkedin.com',
      'youtube.com',
      'tiktok.com',
      'pinterest.com',
      'reddit.com',
    ];
    
    final domain = extractDomain(url);
    
    return socialDomains.any((social) => 
      domain == social || domain.endsWith('.$social'));
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/link.dart';
import '../providers/link_provider.dart';
import '../utils/link_validator.dart';

class LinkCard extends StatelessWidget {
  final Link link;
  final int index;
  final bool showCollection;

  const LinkCard({
    super.key,
    required this.link,
    this.index = 0,
    this.showCollection = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final linkProvider = Provider.of<LinkProvider>(context);
    
    return Slidable(
      key: ValueKey(link.id),
      startActionPane: ActionPane(
        motion: const DrawerMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => _toggleFavorite(context),
            backgroundColor: Colors.amber,
            foregroundColor: Colors.white,
            icon: link.isFavorite ? Icons.star : Icons.star_border,
            label: link.isFavorite ? 'Unfavorite' : 'Favorite',
            borderRadius: const BorderRadius.horizontal(
              left: Radius.circular(16),
            ),
          ),
        ],
      ),
      endActionPane: ActionPane(
        motion: const DrawerMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => _deleteLink(context),
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Delete',
            borderRadius: const BorderRadius.horizontal(
              right: Radius.circular(16),
            ),
          ),
        ],
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: InkWell(
          onTap: () => context.push('/home/<USER>/${link.id}'),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFavicon(),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            link.title,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            link.domain,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (link.isFavorite)
                      Icon(
                        Icons.star_rounded,
                        color: Colors.amber,
                        size: 20,
                      )
                      .animate(onPlay: (controller) => controller.repeat())
                      .shimmer(duration: 2.seconds, delay: 1.seconds),
                  ],
                ),
                if (link.description != null && link.description!.isNotEmpty) ...[  
                  const SizedBox(height: 12),
                  Text(
                    link.description!,
                    style: theme.textTheme.bodySmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                if (link.imageUrl != null) ...[  
                  const SizedBox(height: 12),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      link.imageUrl!,
                      height: 120,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 120,
                          width: double.infinity,
                          color: theme.colorScheme.surfaceVariant,
                          child: Icon(
                            Icons.image_not_supported_outlined,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        );
                      },
                    ),
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (showCollection && link.collectionId != null)
                      _buildCollectionChip(context),
                    const Spacer(),
                    Text(
                      link.formattedCreatedAt,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: const Icon(Icons.open_in_new_rounded, size: 20),
                      onPressed: () => _launchUrl(context),
                      tooltip: 'Open link',
                      style: IconButton.styleFrom(
                        backgroundColor: theme.colorScheme.primaryContainer,
                        foregroundColor: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    )
    .animate(delay: (50 * index).ms)
    .fadeIn(duration: 300.ms, curve: Curves.easeOutQuad)
    .slideY(begin: 0.1, end: 0, duration: 300.ms, curve: Curves.easeOutQuad);
  }

  Widget _buildFavicon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: link.color ?? Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: link.favicon != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                link.favicon!,
                width: 40,
                height: 40,
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Text(
                      link.domain.substring(0, 1).toUpperCase(),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
            )
          : Center(
              child: Text(
                link.domain.substring(0, 1).toUpperCase(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
    );
  }

  Widget _buildCollectionChip(BuildContext context) {
    final linkProvider = Provider.of<LinkProvider>(context, listen: false);
    final collection = linkProvider.collections
        .firstWhere((c) => c.id == link.collectionId, orElse: () => null);
    
    if (collection == null) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: collection.color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(collection.icon, size: 14, color: collection.color),
          const SizedBox(width: 4),
          Text(
            collection.name,
            style: TextStyle(
              fontSize: 12,
              color: collection.color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchUrl(BuildContext context) async {
    final validUrl = LinkValidator.ensureProtocol(link.url);
    final uri = Uri.parse(validUrl);
    
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
      
      // Update last visited time
      final linkProvider = Provider.of<LinkProvider>(context, listen: false);
      final updatedLink = link.copyWith(lastVisited: DateTime.now());
      await linkProvider.updateLink(updatedLink);
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not launch $validUrl'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _toggleFavorite(BuildContext context) async {
    final linkProvider = Provider.of<LinkProvider>(context, listen: false);
    await linkProvider.toggleFavorite(link.id);
  }

  Future<void> _deleteLink(BuildContext context) async {
    final linkProvider = Provider.of<LinkProvider>(context, listen: false);
    await linkProvider.deleteLink(link.id);
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Link deleted'),
          behavior: SnackBarBehavior.floating,
          action: SnackBarAction(
            label: 'Undo',
            onPressed: () {
              // TODO: Implement undo functionality
            },
          ),
        ),
      );
    }
  }
}
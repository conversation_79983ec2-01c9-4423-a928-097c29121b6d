import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../models/link.dart';
import '../screens/home_screen.dart';
import '../screens/collections_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/link_details_screen.dart';
import '../screens/add_link_screen.dart';
import '../screens/search_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/onboarding_screen.dart';
import '../widgets/main_scaffold.dart';
import '../widgets/splash_screen.dart';

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    navigatorKey: _rootNavigatorKey,
    routes: [
      // Splash screen route
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Onboarding route
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      
      // Shell route for bottom navigation
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) => MainScaffold(child: child),
        routes: [
          // Home route
          GoRoute(
            path: '/home',
            builder: (context, state) => const HomeScreen(),
            routes: [
              // Link details route
              GoRoute(
                path: 'link/:id',
                builder: (context, state) {
                  final linkId = state.pathParameters['id']!;
                  return LinkDetailsScreen(linkId: linkId);
                },
              ),
            ],
          ),
          
          // Collections route
          GoRoute(
            path: '/collections',
            builder: (context, state) => const CollectionsScreen(),
          ),
          
          // Profile route
          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfileScreen(),
          ),
        ],
      ),
      
      // Add link route (full screen modal)
      GoRoute(
        path: '/add-link',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const AddLinkScreen(),
      ),
      
      // Search route (full screen)
      GoRoute(
        path: '/search',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const SearchScreen(),
      ),
      
      // Settings route
      GoRoute(
        path: '/settings',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
    
    // Redirect to home if already onboarded
    redirect: (context, state) {
      // TODO: Check if user has completed onboarding
      // const bool isOnboarded = true;
      // if (isOnboarded && state.location == '/onboarding') {
      //   return '/home';
      // }
      return null;
    },
  );
}
import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'link_validator.dart';

class LinkMetadata {
  final String? title;
  final String? description;
  final String? imageUrl;
  final String? favicon;

  LinkMetadata({
    this.title,
    this.description,
    this.imageUrl,
    this.favicon,
  });
}

class LinkParser {
  // Parse URL and extract metadata
  static Future<LinkMetadata> parseUrl(String url) async {
    final validUrl = LinkValidator.ensureProtocol(url);
    
    try {
      final response = await http.get(Uri.parse(validUrl));
      
      if (response.statusCode == 200) {
        final document = html_parser.parse(response.body);
        
        // Extract title
        String? title = _extractTitle(document);
        
        // Extract description
        String? description = _extractDescription(document);
        
        // Extract image
        String? imageUrl = _extractImage(document, validUrl);
        
        // Extract favicon
        String? favicon = _extractFavicon(document, validUrl);
        
        return LinkMetadata(
          title: title,
          description: description,
          imageUrl: imageUrl,
          favicon: favicon,
        );
      }
    } catch (e) {
      print('Error parsing URL: $e');
    }
    
    // Return basic metadata if parsing fails
    return LinkMetadata(
      title: _extractDomainTitle(validUrl),
    );
  }
  
  // Extract title from HTML
  static String? _extractTitle(html_parser.Document document) {
    // Try Open Graph title first
    final ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle != null && ogTitle.attributes['content'] != null) {
      return ogTitle.attributes['content'];
    }
    
    // Try Twitter title
    final twitterTitle = document.querySelector('meta[name="twitter:title"]');
    if (twitterTitle != null && twitterTitle.attributes['content'] != null) {
      return twitterTitle.attributes['content'];
    }
    
    // Fall back to HTML title
    final titleTag = document.querySelector('title');
    if (titleTag != null && titleTag.text.isNotEmpty) {
      return titleTag.text;
    }
    
    return null;
  }
  
  // Extract description from HTML
  static String? _extractDescription(html_parser.Document document) {
    // Try Open Graph description first
    final ogDescription = document.querySelector('meta[property="og:description"]');
    if (ogDescription != null && ogDescription.attributes['content'] != null) {
      return ogDescription.attributes['content'];
    }
    
    // Try Twitter description
    final twitterDescription = document.querySelector('meta[name="twitter:description"]');
    if (twitterDescription != null && twitterDescription.attributes['content'] != null) {
      return twitterDescription.attributes['content'];
    }
    
    // Try meta description
    final metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription != null && metaDescription.attributes['content'] != null) {
      return metaDescription.attributes['content'];
    }
    
    return null;
  }
  
  // Extract image from HTML
  static String? _extractImage(html_parser.Document document, String baseUrl) {
    // Try Open Graph image first
    final ogImage = document.querySelector('meta[property="og:image"]');
    if (ogImage != null && ogImage.attributes['content'] != null) {
      return _resolveUrl(ogImage.attributes['content']!, baseUrl);
    }
    
    // Try Twitter image
    final twitterImage = document.querySelector('meta[name="twitter:image"]');
    if (twitterImage != null && twitterImage.attributes['content'] != null) {
      return _resolveUrl(twitterImage.attributes['content']!, baseUrl);
    }
    
    return null;
  }
  
  // Extract favicon from HTML
  static String? _extractFavicon(html_parser.Document document, String baseUrl) {
    // Try apple-touch-icon first
    final appleIcon = document.querySelector('link[rel="apple-touch-icon"]');
    if (appleIcon != null && appleIcon.attributes['href'] != null) {
      return _resolveUrl(appleIcon.attributes['href']!, baseUrl);
    }
    
    // Try icon
    final icon = document.querySelector('link[rel="icon"]');
    if (icon != null && icon.attributes['href'] != null) {
      return _resolveUrl(icon.attributes['href']!, baseUrl);
    }
    
    // Try shortcut icon
    final shortcutIcon = document.querySelector('link[rel="shortcut icon"]');
    if (shortcutIcon != null && shortcutIcon.attributes['href'] != null) {
      return _resolveUrl(shortcutIcon.attributes['href']!, baseUrl);
    }
    
    // Default favicon location
    try {
      final uri = Uri.parse(baseUrl);
      return '${uri.scheme}://${uri.host}/favicon.ico';
    } catch (e) {
      return null;
    }
  }
  
  // Resolve relative URLs
  static String _resolveUrl(String url, String baseUrl) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    
    try {
      final uri = Uri.parse(baseUrl);
      if (url.startsWith('/')) {
        return '${uri.scheme}://${uri.host}$url';
      } else {
        final path = uri.path.endsWith('/') ? uri.path : uri.path.substring(0, uri.path.lastIndexOf('/') + 1);
        return '${uri.scheme}://${uri.host}$path$url';
      }
    } catch (e) {
      return url;
    }
  }
  
  // Extract domain as title fallback
  static String _extractDomainTitle(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return url;
    }
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/theme_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSection(
            context,
            'Appearance',
            [
              _buildSwitchTile(
                context,
                'Dark Mode',
                Icons.dark_mode_rounded,
                themeProvider.isDarkMode,
                (value) => themeProvider.setThemeMode(
                  value ? ThemeMode.dark : ThemeMode.light,
                ),
              ),
            ],
          )
          .animate()
          .fadeIn(duration: 500.ms, delay: 100.ms)
          .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 100.ms),
          
          const SizedBox(height: 16),
          
          _buildSection(
            context,
            'Notifications',
            [
              _buildSwitchTile(
                context,
                'Link Detection Alerts',
                Icons.notifications_rounded,
                true,
                (value) {
                  // TODO: Implement notification settings
                },
              ),
              _buildSwitchTile(
                context,
                'Daily Reminders',
                Icons.access_time_rounded,
                false,
                (value) {
                  // TODO: Implement reminder settings
                },
              ),
            ],
          )
          .animate()
          .fadeIn(duration: 500.ms, delay: 200.ms)
          .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 200.ms),
          
          const SizedBox(height: 16),
          
          _buildSection(
            context,
            'Data Management',
            [
              _buildActionTile(
                context,
                'Backup Data',
                Icons.backup_rounded,
                () {
                  // TODO: Implement backup functionality
                },
              ),
              _buildActionTile(
                context,
                'Restore Data',
                Icons.restore_rounded,
                () {
                  // TODO: Implement restore functionality
                },
              ),
              _buildActionTile(
                context,
                'Clear All Data',
                Icons.delete_forever_rounded,
                () {
                  _showClearDataDialog(context);
                },
                color: Colors.red,
              ),
            ],
          )
          .animate()
          .fadeIn(duration: 500.ms, delay: 300.ms)
          .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 300.ms),
          
          const SizedBox(height: 16),
          
          _buildSection(
            context,
            'About',
            [
              _buildActionTile(
                context,
                'Version',
                Icons.info_outline_rounded,
                null,
                subtitle: '1.0.0',
              ),
              _buildActionTile(
                context,
                'Help & Support',
                Icons.help_outline_rounded,
                () {
                  _launchUrl('https://example.com/help');
                },
              ),
              _buildActionTile(
                context,
                'Terms of Service',
                Icons.description_outlined,
                () {
                  _launchUrl('https://example.com/terms');
                },
              ),
              _buildActionTile(
                context,
                'Privacy Policy',
                Icons.privacy_tip_outlined,
                () {
                  _launchUrl('https://example.com/privacy');
                },
              ),
            ],
          )
          .animate()
          .fadeIn(duration: 500.ms, delay: 400.ms)
          .slideY(begin: 0.1, end: 0, duration: 500.ms, delay: 400.ms),
        ],
      ),
    );
  }
  
  Widget _buildSection(BuildContext context, String title, List<Widget> children) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ),
        Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: theme.colorScheme.outline.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }
  
  Widget _buildSwitchTile(
    BuildContext context,
    String title,
    IconData icon,
    bool value,
    Function(bool) onChanged,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }
  
  Widget _buildActionTile(
    BuildContext context,
    String title,
    IconData icon,
    Function()? onTap,
    {String? subtitle, Color? color}
  ) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: onTap != null ? const Icon(Icons.chevron_right_rounded) : null,
      onTap: onTap,
    );
  }
  
  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'Are you sure you want to clear all data? This action cannot be undone.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement clear data functionality
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
  
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }
}
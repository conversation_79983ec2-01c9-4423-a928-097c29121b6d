import 'package:flutter/material.dart';

class Collection {
  final String id;
  final String name;
  final String? description;
  final Color color;
  final IconData icon;
  final DateTime createdAt;
  final int linkCount;

  Collection({
    required this.id,
    required this.name,
    this.description,
    required this.color,
    required this.icon,
    required this.createdAt,
    this.linkCount = 0,
  });

  Collection copyWith({
    String? id,
    String? name,
    String? description,
    Color? color,
    IconData? icon,
    DateTime? createdAt,
    int? linkCount,
  }) {
    return Collection(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      createdAt: createdAt ?? this.createdAt,
      linkCount: linkCount ?? this.linkCount,
    );
  }

  // Convert Collection to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color.value,
      'icon': icon.codePoint,
      'iconFontFamily': icon.fontFamily,
      'iconFontPackage': icon.fontPackage,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'linkCount': linkCount,
    };
  }

  // Create Collection from Map
  factory Collection.fromMap(Map<String, dynamic> map) {
    return Collection(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      color: Color(map['color']),
      icon: IconData(
        map['icon'],
        fontFamily: map['iconFontFamily'],
        fontPackage: map['iconFontPackage'],
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      linkCount: map['linkCount'] ?? 0,
    );
  }
}
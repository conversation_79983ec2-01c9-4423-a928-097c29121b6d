import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: const Duration(seconds: 2));
    _controller.forward();
    
    // Check if onboarding has been completed
    _checkOnboardingStatus();
  }
  
  Future<void> _checkOnboardingStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final hasCompletedOnboarding = prefs.getBool('onboarding_completed') ?? false;
    
    // Navigate after animation completes
    Future.delayed(const Duration(seconds: 3), () {
      if (hasCompletedOnboarding) {
        context.go('/home');
      } else {
        context.go('/onboarding');
      }
    });
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo
            SvgPicture.asset(
              'assets/images/logo.svg',
              width: 120,
              height: 120,
            )
            .animate()
            .scale(duration: 1.seconds, curve: Curves.easeOutBack)
            .fade(duration: 800.ms),
            
            const SizedBox(height: 24),
            
            // App Name
            Text(
              'LinkVault',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            )
            .animate(delay: 400.ms)
            .fade(duration: 800.ms)
            .slideY(begin: 0.2, end: 0),
            
            const SizedBox(height: 8),
            
            // Tagline
            Text(
              'Your personal link manager',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            )
            .animate(delay: 600.ms)
            .fade(duration: 800.ms)
            .slideY(begin: 0.2, end: 0),
          ],
        ),
      ),
    );
  }
}